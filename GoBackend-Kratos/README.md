# 🚀 GoBackend HVAC Kratos

## 🎯 Ultra-Modern HVAC CRM with Kratos Framework

### ✨ Features

- 🔥 **Kratos Framework** - Production-ready microservice architecture
- 🤖 **AI Integration** - Gemma-3-4b-it & Bielik V3 models
- 🛠️ **MCP Protocol** - Type-safe LLM tool integration
- 📧 **BillionMail Ready** - Advanced email management
- 🗄️ **PostgreSQL** - Robust database with GORM
- ⚡ **Redis Cache** - High-performance caching
- 🔍 **Jaeger Tracing** - Distributed tracing
- 📊 **Prometheus Metrics** - Comprehensive monitoring
- 🐳 **Docker** - Containerized deployment
- 🔧 **gRPC & HTTP** - Dual transport protocols

### 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HTTP/gRPC     │    │   MCP Server    │    │   AI Models     │
│   Transport     │    │   (LLM Tools)   │    │ Gemma/Bielik    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
┌─────────────────────────────────────────────────────────────────┐
│                    Service Layer                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │ HVAC Service│  │ AI Service  │  │ MCP Service │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
└─────────────────────────────────────────────────────────────────┘
         │
┌─────────────────────────────────────────────────────────────────┐
│                   Business Logic                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │ Customer UC │  │   Job UC    │  │   AI UC     │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
└─────────────────────────────────────────────────────────────────┘
         │
┌─────────────────────────────────────────────────────────────────┐
│                    Data Layer                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │ PostgreSQL  │  │   Redis     │  │ AI Models   │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
└─────────────────────────────────────────────────────────────────┘
```### 🚀 Quick Start

#### Prerequisites
- Go 1.24+
- Docker & Docker Compose
- Protocol Buffers compiler
- NVIDIA GPU (optional, for AI acceleration)

#### 1. Clone & Setup
```bash
git clone <repository>
cd GoBackend-Kratos
chmod +x scripts/*.sh
./scripts/build.sh  # Install required tools & build
```

#### 2. Deploy with AI Models
```bash
./scripts/deploy-with-ai.sh  # Complete deployment with Gemma AI
```

#### 3. Quick Deploy (without AI)
```bash
docker-compose up -d
```

#### 4. Development Mode
```bash
# Start dependencies
docker-compose up -d postgres redis jaeger
docker-compose -f docker-compose.ai.yml up -d ollama

# Setup AI model
./scripts/setup-gemma.sh

# Run locally
go run cmd/server/main.go -conf ./configs
```

### 🔧 API Endpoints

#### HTTP REST API
- `GET /api/v1/customers` - List customers
- `POST /api/v1/customers` - Create customer
- `GET /api/v1/jobs` - List jobs
- `POST /api/v1/jobs` - Create job
- `POST /api/v1/ai/chat` - AI chat
- `POST /api/v1/ai/analyze` - AI analysis

#### gRPC Services
- `HVACService` - Customer & Job management
- `AIService` - AI model interactions

#### MCP Tools (for LLM)
- `create_customer` - Create HVAC customer
- `create_job` - Create HVAC job
- `send_email` - Send emails via BillionMail
- `ai_analyze` - Analyze content with AI
- `hvac_advice` - Get professional HVAC advice using Gemma AI

#### 🤖 AI Integration
- `POST /api/v1/ai/chat` - Chat with Gemma-3-4b-it-qat-q4_0-gguf
- `POST /api/v1/ai/analyze` - Analyze content (sentiment, HVAC issues)
- `GET /api/v1/ai/models` - List available AI models
- Ollama Web UI: http://localhost:3000

### 🎯 AI Model: Gemma-3-4b-it-qat-q4_0-gguf

✅ **Quantized GGUF Model** - Optimized for performance  
✅ **HVAC-Specialized Prompts** - Professional HVAC assistance  
✅ **Sentiment Analysis** - Customer email analysis  
✅ **Issue Classification** - Automatic HVAC problem detection  
✅ **MCP Integration** - Type-safe LLM tools  

Ready for production HVAC AI assistance! 🚀🔥