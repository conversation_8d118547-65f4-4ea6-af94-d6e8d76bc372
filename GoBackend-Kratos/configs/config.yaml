server:
  http:
    addr: 0.0.0.0:8080
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1s

data:
  database:
    driver: postgres
    source: postgres://user:password@localhost:5432/hvac_db?sslmode=disable
  redis:
    addr: localhost:6379
    read_timeout: 0.2s
    write_timeout: 0.2s

ai:
  models:
    gemma:
      endpoint: "http://localhost:11434"
      model_name: "gemma:3b-instruct-q4_0"
      max_tokens: 4096
    bielik:
      endpoint: "http://localhost:11435"
      model_name: "bielik-v3"
      max_tokens: 32768

email:
  smtp:
    host: "localhost"
    port: 587
    username: ""
    password: ""
    from: "<EMAIL>"

mcp:
  server:
    addr: 0.0.0.0:8081
    transport: "stdio"
  tools:
    enabled: true
    hvac_tools: true
    email_tools: true

logging:
  level: "info"
  format: "json"
  output: "stdout"

tracing:
  endpoint: "http://localhost:14268/api/traces"
  sampler: 1.0

metrics:
  addr: 0.0.0.0:9090
  path: "/metrics"