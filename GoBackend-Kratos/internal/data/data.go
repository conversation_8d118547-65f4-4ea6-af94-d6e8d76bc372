package data

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gobackend-hvac-kratos/internal/conf"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(
	NewData,
	NewCustomerRepo,
	NewJobRepo,
	NewAIRepo,
	NewBillionMailService,
)

// Data represents the data layer
type Data struct {
	db  *gorm.DB
	log *log.Helper
}

// NewData creates a new data instance
func NewData(c *conf.Data, logger log.Logger) (*Data, func(), error) {
	log := log.NewHelper(logger)
	
	// Initialize database connection
	db, err := gorm.Open(postgres.Open(c.Database.Source), &gorm.Config{})
	if err != nil {
		return nil, nil, err
	}
	
	// Auto-migrate database schema
	if err := db.AutoMigrate(
		&Customer{},
		&Job{},
	); err != nil {
		return nil, nil, err
	}
	
	d := &Data{
		db:  db,
		log: log,
	}
	
	cleanup := func() {
		log.Info("Closing database connection")
		if sqlDB, err := db.DB(); err == nil {
			sqlDB.Close()
		}
	}
	
	return d, cleanup, nil
}