package biz

import (
	"github.com/go-kratos/kratos/v2/errors"
)

// Customer errors
var (
	ErrCustomerNotFound      = errors.NotFound("CUSTOMER_NOT_FOUND", "customer not found")
	ErrCustomerNameRequired  = errors.BadRequest("CUSTOMER_NAME_REQUIRED", "customer name is required")
	ErrCustomerEmailRequired = errors.BadRequest("CUSTOMER_EMAIL_REQUIRED", "customer email is required")
	ErrInvalidCustomerID     = errors.BadRequest("INVALID_CUSTOMER_ID", "invalid customer ID")
)

// Job errors
var (
	ErrJobNotFound         = errors.NotFound("JOB_NOT_FOUND", "job not found")
	ErrJobTitleRequired    = errors.BadRequest("JOB_TITLE_REQUIRED", "job title is required")
	ErrInvalidJobID        = errors.BadRequest("INVALID_JOB_ID", "invalid job ID")
	ErrInvalidJobStatus    = errors.BadRequest("INVALID_JOB_STATUS", "invalid job status")
	ErrInvalidJobPriority  = errors.BadRequest("INVALID_JOB_PRIORITY", "invalid job priority")
)

// AI errors
var (
	ErrAIModelNotFound     = errors.NotFound("AI_MODEL_NOT_FOUND", "AI model not found")
	ErrAIModelUnavailable  = errors.ServiceUnavailable("AI_MODEL_UNAVAILABLE", "AI model is unavailable")
	ErrInvalidAIRequest    = errors.BadRequest("INVALID_AI_REQUEST", "invalid AI request")
	ErrAIProcessingFailed  = errors.Internal("AI_PROCESSING_FAILED", "AI processing failed")
)

// Email errors
var (
	ErrEmailSendFailed     = errors.Internal("EMAIL_SEND_FAILED", "failed to send email")
	ErrInvalidEmailAddress = errors.BadRequest("INVALID_EMAIL_ADDRESS", "invalid email address")
	ErrEmailNotFound       = errors.NotFound("EMAIL_NOT_FOUND", "email not found")
)

// MCP errors
var (
	ErrMCPServerUnavailable = errors.ServiceUnavailable("MCP_SERVER_UNAVAILABLE", "MCP server is unavailable")
	ErrMCPToolNotFound      = errors.NotFound("MCP_TOOL_NOT_FOUND", "MCP tool not found")
	ErrMCPInvalidRequest    = errors.BadRequest("MCP_INVALID_REQUEST", "invalid MCP request")
)