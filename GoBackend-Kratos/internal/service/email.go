package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "gobackend-hvac-kratos/api/email/v1"
	"gobackend-hvac-kratos/internal/email"
)

// EmailService implements the email service
type EmailService struct {
	pb.UnimplementedEmailServiceServer

	billionMail *email.BillionMailService
	log         *log.Helper
}

// NewEmailService creates a new email service
func NewEmailService(billionMail *email.BillionMailService, logger log.Logger) *EmailService {
	return &EmailService{
		billionMail: billionMail,
		log:         log.NewHelper(logger),
	}
}

// SendEmail sends an email through BillionMail
func (s *EmailService) SendEmail(ctx context.Context, req *pb.SendEmailRequest) (*pb.SendEmailResponse, error) {
	s.log.WithContext(ctx).Infof("Sending email to: %v", req.To)

	// Convert protobuf to internal email message
	msg := &email.EmailMessage{
		From:     req.From,
		To:       req.To,
		CC:       req.Cc,
		BCC:      req.Bcc,
		Subject:  req.Subject,
		Body:     req.Body,
		HTMLBody: req.HtmlBody,
		Priority: req.Priority,
	}

	// Convert attachments
	for _, att := range req.Attachments {
		msg.Attachments = append(msg.Attachments, email.Attachment{
			Filename:    att.Filename,
			ContentType: att.ContentType,
			Size:        att.Size,
			Data:        att.Data,
			URL:         att.Url,
		})
	}

	err := s.billionMail.SendEmail(ctx, msg)
	if err != nil {
		return nil, err
	}

	return &pb.SendEmailResponse{
		EmailId: "email-" + time.Now().Format("20060102150405"),
		Status:  "sent",
		Message: "Email sent successfully",
	}, nil
}// ListEmails retrieves emails from BillionMail
func (s *EmailService) ListEmails(ctx context.Context, req *pb.ListEmailsRequest) (*pb.ListEmailsResponse, error) {
	s.log.WithContext(ctx).Infof("Listing emails: limit=%d, offset=%d", req.Limit, req.Offset)

	emails, err := s.billionMail.ListEmails(ctx, int(req.Limit), int(req.Offset))
	if err != nil {
		return nil, err
	}

	pbEmails := make([]*pb.EmailMessage, len(emails))
	for i, email := range emails {
		pbEmails[i] = s.convertEmailToPB(email)
	}

	return &pb.ListEmailsResponse{
		Emails: pbEmails,
		Total:  int32(len(emails)),
	}, nil
}

// CreateCampaign creates an email marketing campaign
func (s *EmailService) CreateCampaign(ctx context.Context, req *pb.CreateCampaignRequest) (*pb.CreateCampaignResponse, error) {
	s.log.WithContext(ctx).Infof("Creating campaign: %s", req.Name)

	campaign := &email.Campaign{
		Name:        req.Name,
		Subject:     req.Subject,
		Template:    req.Template,
		Recipients:  req.Recipients,
		ScheduledAt: req.ScheduledAt.AsTime(),
	}

	err := s.billionMail.CreateCampaign(ctx, campaign)
	if err != nil {
		return nil, err
	}

	return &pb.CreateCampaignResponse{
		Campaign: s.convertCampaignToPB(campaign),
	}, nil
}

// GetCampaignStats retrieves campaign statistics
func (s *EmailService) GetCampaignStats(ctx context.Context, req *pb.GetCampaignStatsRequest) (*pb.GetCampaignStatsResponse, error) {
	s.log.WithContext(ctx).Infof("Getting campaign stats: %s", req.CampaignId)

	stats, err := s.billionMail.GetCampaignStats(ctx, req.CampaignId)
	if err != nil {
		return nil, err
	}

	return &pb.GetCampaignStatsResponse{
		Stats: &pb.CampaignStats{
			Sent:      int32(stats.Sent),
			Delivered: int32(stats.Delivered),
			Opened:    int32(stats.Opened),
			Clicked:   int32(stats.Clicked),
			Bounced:   int32(stats.Bounced),
			OpenRate:  stats.OpenRate,
			ClickRate: stats.ClickRate,
		},
	}, nil
}// AnalyzeSentiment analyzes email sentiment using AI
func (s *EmailService) AnalyzeSentiment(ctx context.Context, req *pb.AnalyzeSentimentRequest) (*pb.AnalyzeSentimentResponse, error) {
	s.log.WithContext(ctx).Info("Analyzing email sentiment")

	sentiment, confidence, err := s.billionMail.AnalyzeEmailSentiment(ctx, req.Content)
	if err != nil {
		return nil, err
	}

	return &pb.AnalyzeSentimentResponse{
		Sentiment:  sentiment,
		Confidence: confidence,
	}, nil
}

// convertEmailToPB converts internal email to protobuf
func (s *EmailService) convertEmailToPB(email *email.EmailMessage) *pb.EmailMessage {
	pbAttachments := make([]*pb.Attachment, len(email.Attachments))
	for i, att := range email.Attachments {
		pbAttachments[i] = &pb.Attachment{
			Filename:    att.Filename,
			ContentType: att.ContentType,
			Size:        att.Size,
			Data:        att.Data,
			Url:         att.URL,
		}
	}

	return &pb.EmailMessage{
		Id:          email.ID,
		From:        email.From,
		To:          email.To,
		Cc:          email.CC,
		Bcc:         email.BCC,
		Subject:     email.Subject,
		Body:        email.Body,
		HtmlBody:    email.HTMLBody,
		Attachments: pbAttachments,
		Headers:     email.Headers,
		Priority:    email.Priority,
		Timestamp:   timestamppb.New(email.Timestamp),
		Status:      email.Status,
	}
}

// convertCampaignToPB converts internal campaign to protobuf
func (s *EmailService) convertCampaignToPB(campaign *email.Campaign) *pb.Campaign {
	return &pb.Campaign{
		Id:          campaign.ID,
		Name:        campaign.Name,
		Subject:     campaign.Subject,
		Template:    campaign.Template,
		Recipients:  campaign.Recipients,
		ScheduledAt: timestamppb.New(campaign.ScheduledAt),
		Status:      campaign.Status,
		Stats: &pb.CampaignStats{
			Sent:      int32(campaign.Stats.Sent),
			Delivered: int32(campaign.Stats.Delivered),
			Opened:    int32(campaign.Stats.Opened),
			Clicked:   int32(campaign.Stats.Clicked),
			Bounced:   int32(campaign.Stats.Bounced),
			OpenRate:  campaign.Stats.OpenRate,
			ClickRate: campaign.Stats.ClickRate,
		},
	}
}