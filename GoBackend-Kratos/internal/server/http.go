package server

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/transport/http"

	hvacv1 "gobackend-hvac-kratos/api/hvac/v1"
	aiv1 "gobackend-hvac-kratos/api/ai/v1"
	emailv1 "gobackend-hvac-kratos/api/email/v1"
	"gobackend-hvac-kratos/internal/conf"
	"gobackend-hvac-kratos/internal/service"
)

// NewHTTPServer creates a new HTTP server
func NewHTTPServer(
	c *conf.Server,
	hvacService *service.HVACService,
	aiService *service.AIService,
	emailService *service.EmailService,
	logger log.Logger,
) *http.Server {
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			tracing.Server(),
			logging.Server(logger),
			metrics.Server(),
		),
	}

	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}

	srv := http.NewServer(opts...)

	// Register HVAC service
	hvacv1.RegisterHVACServiceHTTPServer(srv, hvacService)
	
	// Register AI service
	aiv1.RegisterAIServiceHTTPServer(srv, aiService)
	
	// Register Email service
	emailv1.RegisterEmailServiceHTTPServer(srv, emailService)

	return srv
}